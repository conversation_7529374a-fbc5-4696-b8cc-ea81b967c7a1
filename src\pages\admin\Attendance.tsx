import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, CheckCircle, XCircle, Calendar } from "lucide-react";

const attendanceData = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Cashier",
    checkIn: "09:00 AM",
    checkOut: "05:00 PM",
    status: "Present",
    hours: "8.0",
    shop: "Downtown",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Manager",
    checkIn: "08:30 AM",
    checkOut: "06:30 PM",
    status: "Present",
    hours: "10.0",
    shop: "Mall",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Sales Associate",
    checkIn: "-",
    checkOut: "-",
    status: "Absent",
    hours: "0.0",
    shop: "Airport",
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Cashier",
    checkIn: "09:15 AM",
    checkOut: "Currently Working",
    status: "Present",
    hours: "6.5",
    shop: "Downtown",
  },
];

export default function Attendance() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Attendance</h2>
          <p className="text-muted-foreground">
            Track employee attendance and working hours across all locations.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            View Calendar
          </Button>
          <Button>Export Report</Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Present Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">24</div>
            <p className="text-xs text-muted-foreground">out of 28 employees</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Absent Today</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">4</div>
            <p className="text-xs text-muted-foreground">14.3% absence rate</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">192</div>
            <p className="text-xs text-muted-foreground">hours worked today</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Time</CardTitle>
            <CheckCircle className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">22</div>
            <p className="text-xs text-muted-foreground">91.7% punctuality</p>
          </CardContent>
        </Card>
      </div>

      {/* Attendance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Today's Attendance</CardTitle>
          <CardDescription>Real-time attendance tracking for all employees</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {attendanceData.map((employee) => (
              <div key={employee.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src="" alt={employee.name} />
                    <AvatarFallback>
                      {employee.name.split(" ").map(n => n[0]).join("").toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{employee.name}</p>
                    <p className="text-sm text-muted-foreground">{employee.role} • {employee.shop}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <p className="text-muted-foreground">Check In</p>
                    <p className="font-medium">{employee.checkIn}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground">Check Out</p>
                    <p className="font-medium">{employee.checkOut}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground">Hours</p>
                    <p className="font-medium">{employee.hours}h</p>
                  </div>
                  <Badge variant={employee.status === "Present" ? "default" : "destructive"}>
                    {employee.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}