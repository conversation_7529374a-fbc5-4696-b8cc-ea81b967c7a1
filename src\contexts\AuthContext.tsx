import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase, Profile, AuthUser } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'

interface AuthContextType {
  user: AuthUser | null
  session: Session | null
  loading: boolean
  actionLoading: boolean
  signIn: (email: string, password: string) => Promise<{ error?: AuthError }>
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error?: AuthError }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error?: Error }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    let mounted = true

    // Get initial session
    const initializeAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting session:', error)
          if (mounted) {
            setLoading(false)
          }
          return
        }

        if (mounted) {
          setSession(session)
          if (session?.user) {
            await fetchUserProfile(session.user)
          } else {
            setLoading(false)
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    initializeAuth()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return

      console.log('Auth state change:', event, session?.user?.email)
      setSession(session)

      if (session?.user) {
        await fetchUserProfile(session.user)
      } else {
        setUser(null)
        setLoading(false)
      }
    })

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  const fetchUserProfile = async (authUser: User) => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authUser.id)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile:', error)
        // Don't show toast for profile errors during auth initialization
        // as it might be a new user without a profile yet
      }

      setUser({
        id: authUser.id,
        email: authUser.email || '',
        profile: profile || undefined
      })
    } catch (error) {
      console.error('Error in fetchUserProfile:', error)
      // Still set the user even if profile fetch fails
      setUser({
        id: authUser.id,
        email: authUser.email || '',
        profile: undefined
      })
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    setActionLoading(true)
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        toast({
          title: "Login Failed",
          description: error.message,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Login Successful",
          description: "Welcome back!",
        })
      }

      return { error }
    } catch (error) {
      const authError = error as AuthError
      toast({
        title: "Login Failed",
        description: authError.message || "An unexpected error occurred",
        variant: "destructive",
      })
      return { error: authError }
    } finally {
      setActionLoading(false)
    }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    setActionLoading(true)
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      })

      if (error) {
        toast({
          title: "Registration Failed",
          description: error.message,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Registration Successful",
          description: "Please check your email to verify your account.",
        })
      }

      return { error }
    } catch (error) {
      const authError = error as AuthError
      toast({
        title: "Registration Failed",
        description: authError.message || "An unexpected error occurred",
        variant: "destructive",
      })
      return { error: authError }
    } finally {
      setActionLoading(false)
    }
  }

  const signOut = async () => {
    setActionLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        toast({
          title: "Error",
          description: "Failed to sign out",
          variant: "destructive",
        })
      } else {
        toast({
          title: "Signed Out",
          description: "You have been signed out successfully",
        })
      }
    } catch (error) {
      console.error('Sign out error:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred during sign out",
        variant: "destructive",
      })
    } finally {
      setActionLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user?.id) {
      return { error: new Error('No user logged in') }
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)

      if (error) {
        toast({
          title: "Error",
          description: "Failed to update profile",
          variant: "destructive",
        })
        return { error }
      }

      // Refresh user profile
      if (session?.user) {
        await fetchUserProfile(session.user)
      }

      toast({
        title: "Success",
        description: "Profile updated successfully",
      })

      return {}
    } catch (error) {
      const err = error as Error
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      })
      return { error: err }
    }
  }

  const value = {
    user,
    session,
    loading,
    actionLoading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
