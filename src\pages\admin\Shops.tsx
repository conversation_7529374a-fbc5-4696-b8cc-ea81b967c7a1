import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Store, MapPin, Users, DollarSign } from "lucide-react";

const shops = [
  {
    id: 1,
    name: "Rainbow Station Downtown",
    address: "123 Main St, Downtown",
    manager: "John Smith",
    status: "Active",
    dailySales: "$2,450",
    employees: 8,
  },
  {
    id: 2,
    name: "Rainbow Station Mall",
    address: "456 Shopping Center Blvd",
    manager: "<PERSON>",
    status: "Active",
    dailySales: "$3,120",
    employees: 12,
  },
  {
    id: 3,
    name: "Rainbow Station Airport",
    address: "789 Airport Terminal",
    manager: "<PERSON> Davis",
    status: "Maintenance",
    dailySales: "$0",
    employees: 6,
  },
];

export default function Shops() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Shops</h2>
          <p className="text-muted-foreground">
            Manage all your retail locations and monitor their performance.
          </p>
        </div>
        <Button>Add New Shop</Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {shops.map((shop) => (
          <Card key={shop.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <Store className="h-8 w-8 text-primary" />
                <Badge variant={shop.status === "Active" ? "default" : "destructive"}>
                  {shop.status}
                </Badge>
              </div>
              <CardTitle className="text-lg">{shop.name}</CardTitle>
              <CardDescription className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {shop.address}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">Daily Sales</p>
                    <p className="font-semibold">{shop.dailySales}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">Employees</p>
                    <p className="font-semibold">{shop.employees}</p>
                  </div>
                </div>
              </div>
              <div className="pt-2 border-t">
                <p className="text-sm text-muted-foreground">Manager</p>
                <p className="font-medium">{shop.manager}</p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  View Details
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}